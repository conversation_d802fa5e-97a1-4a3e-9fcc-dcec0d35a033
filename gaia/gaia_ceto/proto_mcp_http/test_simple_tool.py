#!/usr/bin/env python3
"""
Simple test script to verify enhanced MCP server basic functionality
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

async def test_simple_tools():
    """Test calling simple tools on the enhanced MCP server."""
    print("Testing Enhanced MCP Server simple tools...")
    
    client = MCPClientLib()
    
    try:
        # Connect to the enhanced MCP server
        server_url = "http://localhost:9000/mcp"
        print(f'Connecting to enhanced server: {server_url}')
        
        success = await client.connect_to_server(server_url)
        
        if success:
            print('✅ Connection successful!')
            print(f'Available tools: {[tool["name"] for tool in client.available_tools]}')
            
            # Test the simple echostring tool first
            print('\nTesting echostring tool...')
            result = await client.call_tool(
                tool_name='echostring',
                tool_input={'phrase': 'Hello World'},
                tool_call_id='test_echo',
                timeout=30.0
            )
            
            print(f'Echostring result:')
            print(f'  Success: {result.success}')
            print(f'  Error: {result.error}')
            print(f'  Content: {result.content}')
            print(f'  Execution time: {result.execution_time}')
            
            # Test server_status tool
            print('\nTesting server_status tool...')
            result = await client.call_tool(
                tool_name='server_status',
                tool_input={},
                tool_call_id='test_status',
                timeout=30.0
            )
            
            print(f'Server status result:')
            print(f'  Success: {result.success}')
            if result.success:
                print(f'  Status preview: {str(result.content)[:200]}...')
            else:
                print(f'  Error: {result.error}')
                
        else:
            print('❌ Connection failed - make sure the enhanced MCP server is running on port 9000')
            
    except Exception as e:
        print(f'Exception: {e}')
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("Cleaning up...")
        try:
            await client.cleanup()
        except Exception as cleanup_e:
            print(f"Cleanup error (non-critical): {cleanup_e}")

if __name__ == "__main__":
    asyncio.run(test_simple_tools())
