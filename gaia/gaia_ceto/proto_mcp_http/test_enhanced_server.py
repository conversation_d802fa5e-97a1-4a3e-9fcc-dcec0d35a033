#!/usr/bin/env python3
"""
Test script for the Enhanced MCP Server
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from mcp_http_server_enhanced import EnhancedMCPServer

async def test_enhanced_server():
    """Test the enhanced MCP server initialization and tool registration."""
    print("Testing Enhanced MCP Server...")
    
    # Create the server
    server = EnhancedMCPServer("server_config.json")
    
    try:
        # Initialize the server (this loads third-party servers)
        print("Initializing server...")
        await server.initialize()
        
        # Check if third-party tools were registered
        print(f"Third-party tools registered: {len(server.third_party_tools)}")
        for tool_name, tool_info in server.third_party_tools.items():
            print(f"  - {tool_name} (from {tool_info['server_id']})")
        
        # Test the server status tool
        print("\nTesting server_status tool...")
        try:
            # We can't easily test the FastMCP tools directly, but we can check the structure
            print("Server initialized successfully!")
            
        except Exception as e:
            print(f"Error testing tools: {e}")
            
    except Exception as e:
        print(f"Error during server initialization: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("Cleaning up...")
        await server.cleanup()

if __name__ == "__main__":
    asyncio.run(test_enhanced_server())
