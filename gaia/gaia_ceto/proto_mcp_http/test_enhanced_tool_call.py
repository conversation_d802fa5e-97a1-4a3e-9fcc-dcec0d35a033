#!/usr/bin/env python3
"""
Test script to verify enhanced MCP server tool calls work
"""

import asyncio
import sys
import os
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

async def test_enhanced_server_tool_call():
    """Test calling tools on the enhanced MCP server."""
    print("Testing Enhanced MCP Server tool calls...")
    
    client = MCPClientLib()
    
    try:
        # Connect to the enhanced MCP server
        server_url = "http://localhost:9000/mcp"
        print(f'Connecting to enhanced server: {server_url}')
        
        success = await client.connect_to_server(server_url)
        
        if success:
            print('✅ Connection successful!')
            print(f'Available tools: {[tool["name"] for tool in client.available_tools]}')
            
            # Test the web_firecrawl_scrape tool
            print('\nTesting web_firecrawl_scrape tool...')
            result = await client.call_tool(
                tool_name='web_firecrawl_scrape',
                tool_input={'url': 'https://httpbin.org/html', 'formats': ['markdown']},
                tool_call_id='test_call',
                timeout=120.0
            )
            
            print(f'Tool call result:')
            print(f'  Success: {result.success}')
            print(f'  Error: {result.error}')
            print(f'  Execution time: {result.execution_time}')
            if result.success and result.content:
                content_preview = str(result.content)[:200] + '...' if len(str(result.content)) > 200 else str(result.content)
                print(f'  Content preview: {content_preview}')
        else:
            print('❌ Connection failed - make sure the enhanced MCP server is running on port 9000')
            print('Start it with: python mcp_http_server_enhanced.py --port 9000')
            
    except Exception as e:
        print(f'Exception: {e}')
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("Cleaning up...")
        try:
            await client.cleanup()
        except Exception as cleanup_e:
            print(f"Cleanup error (non-critical): {cleanup_e}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_server_tool_call())
