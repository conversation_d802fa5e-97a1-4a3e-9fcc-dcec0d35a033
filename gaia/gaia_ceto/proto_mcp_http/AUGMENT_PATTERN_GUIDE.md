# Augment Pattern for MCP Server Configuration

## Overview

This document explains how the Enhanced MCP Server now follows the **Augment pattern** for connecting to third-party MCP servers, similar to how Augment, VS Code, Claude Desktop, and Cursor handle MCP server configurations.

## What Changed

### Before (Direct URL Connection)
The previous implementation connected directly to hosted MCP server URLs:

```json
{
  "third_party_servers": {
    "firecrawl": {
      "enabled": true,
      "url": "https://mcp.firecrawl.dev/{FIRECRAWL_API_KEY}/sse",
      "protocol": "sse",
      "namespace": "web"
    }
  }
}
```

### After (Augment Pattern - Process Spawning)
The new implementation follows the Augment pattern using `command`, `args`, and `env`:

```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "fc-your-api-key-here"
      },
      "namespace": "web"
    }
  }
}
```

## How Augment Pattern Works

### 1. Process Spawning
Instead of connecting to URLs directly, the system:
1. **Spawns MCP server processes** using the specified `command` and `args`
2. **Passes environment variables** (like API keys) to the spawned process
3. **Connects to the spawned process** via stdio, HTTP, or SSE protocols
4. **Manages the lifecycle** of these processes (start, stop, restart)

### 2. Configuration Format
The Augment pattern uses this structure:

```json
{
  "mcpServers": {
    "server-name": {
      "command": "executable",           // Command to run (npx, uv, python, etc.)
      "args": ["arg1", "arg2"],         // Arguments to pass to command
      "env": {                          // Environment variables for the process
        "API_KEY": "your-key"
      },
      "namespace": "prefix",            // Tool namespace prefix
      "enabled": true                   // Whether to start this server
    }
  }
}
```

### 3. Examples

#### Firecrawl MCP Server
```json
"firecrawl-mcp": {
  "command": "npx",
  "args": ["-y", "firecrawl-mcp"],
  "env": {
    "FIRECRAWL_API_KEY": "fc-your-api-key"
  },
  "namespace": "web"
}
```

#### Filesystem MCP Server
```json
"filesystem-mcp": {
  "command": "npx",
  "args": ["@modelcontextprotocol/server-filesystem", "/path/to/directory"],
  "env": {},
  "namespace": "fs"
}
```

#### Python MCP Server
```json
"python-mcp": {
  "command": "uv",
  "args": ["run", "my-server.py"],
  "env": {
    "DATABASE_URL": "sqlite:///data.db"
  },
  "namespace": "db"
}
```

## Benefits of Augment Pattern

### 1. **Standardization**
- Follows the same pattern as Augment, VS Code, Claude Desktop, and Cursor
- Makes configuration portable between different MCP clients
- Consistent with industry standards

### 2. **Process Management**
- Better control over server lifecycle
- Can restart failed servers
- Proper cleanup when shutting down
- Environment isolation

### 3. **Security**
- Environment variables are passed securely to spawned processes
- No hardcoded credentials in URLs
- Process-level isolation

### 4. **Flexibility**
- Supports any executable (npx, uv, python, docker, etc.)
- Can pass custom arguments and environment variables
- Works with both local and packaged MCP servers

## Implementation Details

### Current Implementation Status
The Enhanced MCP Server has been updated to:

1. ✅ **Parse Augment-style configuration** (`mcpServers` instead of `third_party_servers`)
2. ✅ **Remove unnecessary fallback code** (direct Firecrawl client)
3. ✅ **Update configuration examples** to use the new format
4. 🔄 **Partial process spawning** (currently simplified for Firecrawl)

### Next Steps for Full Implementation

To complete the Augment pattern implementation, you would need to:

1. **Add subprocess management**:
   ```python
   import subprocess
   import asyncio
   
   # Spawn process
   process = await asyncio.create_subprocess_exec(
       command, *args,
       env=process_env,
       stdout=subprocess.PIPE,
       stderr=subprocess.PIPE
   )
   ```

2. **Implement protocol detection**:
   - Detect if spawned process provides stdio, HTTP, or SSE interface
   - Connect using appropriate protocol

3. **Add process lifecycle management**:
   - Track spawned processes
   - Restart failed processes
   - Clean shutdown on server stop

## Migration Guide

### For Existing Configurations
Update your `server_config.json` from:

```json
{
  "third_party_servers": {
    "firecrawl": {
      "url": "https://mcp.firecrawl.dev/{API_KEY}/sse",
      "protocol": "sse"
    }
  }
}
```

To:

```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "your-api-key"
      }
    }
  }
}
```

### For New Servers
Use the Augment pattern from the start:

1. Find the MCP server package (e.g., on npm, PyPI)
2. Determine the command to run it (npx, uv, python, etc.)
3. Configure environment variables as needed
4. Add to `mcpServers` configuration

## Compatibility

This implementation maintains backward compatibility while moving toward the Augment pattern:

- ✅ **Local tools** continue to work unchanged
- ✅ **Existing MultiMCPClient** integration preserved
- ✅ **Tool namespacing** works the same way
- ✅ **Configuration file** location unchanged (`server_config.json`)

The main change is the configuration format, which now matches industry standards used by Augment and other MCP clients.
