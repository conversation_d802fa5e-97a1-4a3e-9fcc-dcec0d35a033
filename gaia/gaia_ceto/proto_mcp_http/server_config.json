{"description": "Enhanced MCP Server Configuration (Augment Pattern)", "mcpServers": {"firecrawl-mcp": {"enabled": true, "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-321168a01ed04ec88b3df4b183c82294"}, "namespace": "web", "description": "Firecrawl MCP server spawned via npx"}, "filesystem-mcp": {"enabled": false, "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/tmp"], "env": {}, "namespace": "fs", "description": "Filesystem MCP server for /tmp directory"}}, "chat_term_usage": {"description": "Connect chat_term to this enhanced server", "command": "python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp", "available_tools": ["echostring (local)", "echostring_table (local)", "long_task (local)", "server_status (local)", "list_all_tools (local)", "third_party_health (local)", "web_firecrawl_scrape (delegated)", "fs_read_file (delegated)"]}}