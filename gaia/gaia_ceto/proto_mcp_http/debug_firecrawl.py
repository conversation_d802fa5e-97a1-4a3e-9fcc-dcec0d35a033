#!/usr/bin/env python3
"""
Debug script to test Firecrawl connection directly
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib

async def debug_firecrawl():
    """Debug Firecrawl connection and tool call."""
    print("Testing direct Firecrawl connection...")
    
    client = MCPClientLib()
    
    try:
        # Connect to Firecrawl
        api_key = 'fc-321168a01ed04ec88b3df4b183c82294'
        server_url = f'https://mcp.firecrawl.dev/{api_key}/sse'
        print(f'Connecting to: {server_url}')
        
        success = await client.connect_to_server(server_url)
        
        if success:
            print('✅ Connection successful!')
            print(f'Available tools: {[tool["name"] for tool in client.available_tools]}')
            
            # Test with the original failing URL
            print('\nTesting firecrawl_scrape tool with agfunder.com...')
            result = await client.call_tool(
                tool_name='firecrawl_scrape',
                tool_input={'url': 'https://agfunder.com', 'formats': ['markdown']},
                tool_call_id='debug_call',
                timeout=120.0  # 2 minutes timeout
            )
            
            print(f'Tool call result:')
            print(f'  Success: {result.success}')
            print(f'  Error: {result.error}')
            print(f'  Execution time: {result.execution_time}')
            if result.success and result.content:
                content_preview = str(result.content)[:200] + '...' if len(str(result.content)) > 200 else str(result.content)
                print(f'  Content preview: {content_preview}')
        else:
            print('❌ Connection failed')
            
    except Exception as e:
        print(f'Exception: {e}')
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("Cleaning up...")
        try:
            await client.cleanup()
        except Exception as cleanup_e:
            print(f"Cleanup error (non-critical): {cleanup_e}")

if __name__ == "__main__":
    asyncio.run(debug_firecrawl())
